{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/step.ts"], "sourcesContent": ["import type { TestInfo } from '@playwright/test'\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { test } from '@playwright/test'\n\nexport interface StepProps {\n  category: string\n  title: string\n  apiName?: string\n  params?: Record<string, string | number | boolean | null | undefined>\n}\n\ntype Complete = (result: { error?: any }) => void\n\nexport async function step<T>(\n  _testInfo: TestInfo,\n  props: StepProps,\n  handler: (complete: Complete) => Promise<Awaited<T>>\n): Promise<Awaited<T>> {\n  let result: Awaited<T>\n  let reportedError: any\n  try {\n    await test.step(props.title, async () => {\n      result = await handler(({ error }) => {\n        reportedError = error\n        if (reportedError) {\n          throw reportedError\n        }\n      })\n    })\n  } catch (error) {\n    if (error !== reportedError) {\n      throw error\n    }\n  }\n  return result!\n}\n"], "names": ["step", "_testInfo", "props", "handler", "result", "reportedError", "test", "title", "error"], "mappings": ";;;;+BAasBA;;;eAAAA;;;sBAXD;AAWd,eAAeA,KACpBC,SAAmB,EACnBC,KAAgB,EAChBC,OAAoD;IAEpD,IAAIC;IACJ,IAAIC;IACJ,IAAI;QACF,MAAMC,UAAI,CAACN,IAAI,CAACE,MAAMK,KAAK,EAAE;YAC3BH,SAAS,MAAMD,QAAQ,CAAC,EAAEK,KAAK,EAAE;gBAC/BH,gBAAgBG;gBAChB,IAAIH,eAAe;oBACjB,MAAMA;gBACR;YACF;QACF;IACF,EAAE,OAAOG,OAAO;QACd,IAAIA,UAAUH,eAAe;YAC3B,MAAMG;QACR;IACF;IACA,OAAOJ;AACT", "ignoreList": [0]}