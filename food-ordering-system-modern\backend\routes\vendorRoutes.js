const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// @desc    Vendor dashboard
// @route   GET /api/vendors/dashboard
// @access  Private (Vendor)
router.get('/dashboard', protect, authorize('vendor'), async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      message: 'Vendor dashboard endpoint',
      data: {}
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
