const express = require('express');
const { body, validationResult } = require('express-validator');
const Restaurant = require('../models/Restaurant');
const Food = require('../models/Food');
const { protect, authorize, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// @desc    Get all restaurants
// @route   GET /api/restaurants
// @access  Public
router.get('/', optionalAuth, async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 12,
      search,
      cuisine,
      minRating,
      sortBy = 'ratings.average',
      sortOrder = 'desc',
      lat,
      lng,
      radius = 10
    } = req.query;

    const query = { status: 'approved', isOpen: true };

    // Search by name or description
    if (search) {
      query.$text = { $search: search };
    }

    // Filter by cuisine
    if (cuisine) {
      query.cuisine = { $in: cuisine.split(',') };
    }

    // Filter by minimum rating
    if (minRating) {
      query['ratings.average'] = { $gte: parseFloat(minRating) };
    }

    // Location-based filtering
    if (lat && lng) {
      query['location.coordinates'] = {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [parseFloat(lng), parseFloat(lat)]
          },
          $maxDistance: parseFloat(radius) * 1000 // Convert km to meters
        }
      };
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 },
      populate: [
        { path: 'owner', select: 'name email' }
      ]
    };

    const restaurants = await Restaurant.find(query)
      .populate(options.populate)
      .sort(options.sort)
      .limit(options.limit * 1)
      .skip((options.page - 1) * options.limit);

    const total = await Restaurant.countDocuments(query);

    res.status(200).json({
      success: true,
      count: restaurants.length,
      total,
      pages: Math.ceil(total / options.limit),
      currentPage: options.page,
      data: restaurants
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get single restaurant
// @route   GET /api/restaurants/:id
// @access  Public
router.get('/:id', optionalAuth, async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findById(req.params.id)
      .populate('owner', 'name email');

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'Restaurant not found'
      });
    }

    // Get restaurant's food items
    const foods = await Food.find({ 
      restaurant: restaurant._id, 
      isAvailable: true 
    }).sort({ category: 1, name: 1 });

    res.status(200).json({
      success: true,
      data: {
        restaurant,
        foods
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Create restaurant
// @route   POST /api/restaurants
// @access  Private (Vendor)
router.post('/', protect, authorize('vendor', 'admin'), [
  body('name').trim().isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),
  body('description').trim().isLength({ min: 10, max: 500 }).withMessage('Description must be between 10 and 500 characters'),
  body('email').isEmail().withMessage('Please provide a valid email'),
  body('phone').matches(/^\d{10}$/).withMessage('Please provide a valid 10-digit phone number'),
  body('cuisine').isArray({ min: 1 }).withMessage('At least one cuisine type is required'),
  body('location.address.street').notEmpty().withMessage('Street address is required'),
  body('location.address.city').notEmpty().withMessage('City is required'),
  body('location.coordinates.latitude').isFloat({ min: -90, max: 90 }).withMessage('Valid latitude is required'),
  body('location.coordinates.longitude').isFloat({ min: -180, max: 180 }).withMessage('Valid longitude is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Check if user already has a restaurant
    const existingRestaurant = await Restaurant.findOne({ owner: req.user._id });
    if (existingRestaurant) {
      return res.status(400).json({
        success: false,
        message: 'You already have a restaurant registered'
      });
    }

    const restaurantData = {
      ...req.body,
      owner: req.user._id
    };

    const restaurant = await Restaurant.create(restaurantData);

    res.status(201).json({
      success: true,
      message: 'Restaurant created successfully. Pending approval.',
      data: restaurant
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update restaurant
// @route   PUT /api/restaurants/:id
// @access  Private (Owner/Admin)
router.put('/:id', protect, async (req, res, next) => {
  try {
    let restaurant = await Restaurant.findById(req.params.id);

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'Restaurant not found'
      });
    }

    // Check ownership
    if (restaurant.owner.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this restaurant'
      });
    }

    restaurant = await Restaurant.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      message: 'Restaurant updated successfully',
      data: restaurant
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Delete restaurant
// @route   DELETE /api/restaurants/:id
// @access  Private (Owner/Admin)
router.delete('/:id', protect, async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findById(req.params.id);

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'Restaurant not found'
      });
    }

    // Check ownership
    if (restaurant.owner.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this restaurant'
      });
    }

    await restaurant.deleteOne();

    res.status(200).json({
      success: true,
      message: 'Restaurant deleted successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get restaurants by location
// @route   GET /api/restaurants/nearby
// @access  Public
router.get('/nearby/:lat/:lng', async (req, res, next) => {
  try {
    const { lat, lng } = req.params;
    const { radius = 10, limit = 20 } = req.query;

    const restaurants = await Restaurant.find({
      status: 'approved',
      isOpen: true,
      'location.coordinates': {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [parseFloat(lng), parseFloat(lat)]
          },
          $maxDistance: parseFloat(radius) * 1000
        }
      }
    })
    .limit(parseInt(limit))
    .populate('owner', 'name');

    res.status(200).json({
      success: true,
      count: restaurants.length,
      data: restaurants
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Toggle restaurant status (open/closed)
// @route   PATCH /api/restaurants/:id/toggle-status
// @access  Private (Owner/Admin)
router.patch('/:id/toggle-status', protect, async (req, res, next) => {
  try {
    const restaurant = await Restaurant.findById(req.params.id);

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'Restaurant not found'
      });
    }

    // Check ownership
    if (restaurant.owner.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this restaurant'
      });
    }

    restaurant.isOpen = !restaurant.isOpen;
    await restaurant.save();

    res.status(200).json({
      success: true,
      message: `Restaurant ${restaurant.isOpen ? 'opened' : 'closed'} successfully`,
      data: { isOpen: restaurant.isOpen }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
