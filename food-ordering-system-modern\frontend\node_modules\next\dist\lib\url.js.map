{"version": 3, "sources": ["../../src/lib/url.ts"], "sourcesContent": ["import type { UrlWithParsedQuery } from 'url'\nimport { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\n\nconst DUMMY_ORIGIN = 'http://n'\n\nexport function isFullStringUrl(url: string) {\n  return /https?:\\/\\//.test(url)\n}\n\nexport function parseUrl(url: string): URL | undefined {\n  let parsed: URL | undefined = undefined\n  try {\n    parsed = new URL(url, DUMMY_ORIGIN)\n  } catch {}\n  return parsed\n}\n\nexport function parseReqUrl(url: string): UrlWithParsedQuery | undefined {\n  const parsedUrl: URL | undefined = parseUrl(url)\n\n  if (!parsedUrl) {\n    return\n  }\n\n  const query: Record<string, string | string[]> = {}\n\n  for (const key of parsedUrl.searchParams.keys()) {\n    const values = parsedUrl.searchParams.getAll(key)\n    query[key] = values.length > 1 ? values : values[0]\n  }\n\n  const legacyUrl: UrlWithParsedQuery = {\n    query,\n    hash: parsedUrl.hash,\n    search: parsedUrl.search,\n    path: parsedUrl.pathname,\n    pathname: parsedUrl.pathname,\n    href: `${parsedUrl.pathname}${parsedUrl.search}${parsedUrl.hash}`,\n    host: '',\n    hostname: '',\n    auth: '',\n    protocol: '',\n    slashes: null,\n    port: '',\n  }\n  return legacyUrl\n}\n\nexport function stripNextRscUnionQuery(relativeUrl: string): string {\n  const urlInstance = new URL(relativeUrl, DUMMY_ORIGIN)\n  urlInstance.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  return urlInstance.pathname + urlInstance.search\n}\n"], "names": ["isFullStringUrl", "parseReqUrl", "parseUrl", "stripNextRscUnionQuery", "DUMMY_ORIGIN", "url", "test", "parsed", "undefined", "URL", "parsedUrl", "query", "key", "searchParams", "keys", "values", "getAll", "length", "legacyUrl", "hash", "search", "path", "pathname", "href", "host", "hostname", "auth", "protocol", "slashes", "port", "relativeUrl", "urlInstance", "delete", "NEXT_RSC_UNION_QUERY"], "mappings": ";;;;;;;;;;;;;;;;;IAKgBA,eAAe;eAAfA;;IAYAC,WAAW;eAAXA;;IARAC,QAAQ;eAARA;;IAuCAC,sBAAsB;eAAtBA;;;kCA/CqB;AAErC,MAAMC,eAAe;AAEd,SAASJ,gBAAgBK,GAAW;IACzC,OAAO,cAAcC,IAAI,CAACD;AAC5B;AAEO,SAASH,SAASG,GAAW;IAClC,IAAIE,SAA0BC;IAC9B,IAAI;QACFD,SAAS,IAAIE,IAAIJ,KAAKD;IACxB,EAAE,OAAM,CAAC;IACT,OAAOG;AACT;AAEO,SAASN,YAAYI,GAAW;IACrC,MAAMK,YAA6BR,SAASG;IAE5C,IAAI,CAACK,WAAW;QACd;IACF;IAEA,MAAMC,QAA2C,CAAC;IAElD,KAAK,MAAMC,OAAOF,UAAUG,YAAY,CAACC,IAAI,GAAI;QAC/C,MAAMC,SAASL,UAAUG,YAAY,CAACG,MAAM,CAACJ;QAC7CD,KAAK,CAACC,IAAI,GAAGG,OAAOE,MAAM,GAAG,IAAIF,SAASA,MAAM,CAAC,EAAE;IACrD;IAEA,MAAMG,YAAgC;QACpCP;QACAQ,MAAMT,UAAUS,IAAI;QACpBC,QAAQV,UAAUU,MAAM;QACxBC,MAAMX,UAAUY,QAAQ;QACxBA,UAAUZ,UAAUY,QAAQ;QAC5BC,MAAM,GAAGb,UAAUY,QAAQ,GAAGZ,UAAUU,MAAM,GAAGV,UAAUS,IAAI,EAAE;QACjEK,MAAM;QACNC,UAAU;QACVC,MAAM;QACNC,UAAU;QACVC,SAAS;QACTC,MAAM;IACR;IACA,OAAOX;AACT;AAEO,SAASf,uBAAuB2B,WAAmB;IACxD,MAAMC,cAAc,IAAItB,IAAIqB,aAAa1B;IACzC2B,YAAYlB,YAAY,CAACmB,MAAM,CAACC,sCAAoB;IAEpD,OAAOF,YAAYT,QAAQ,GAAGS,YAAYX,MAAM;AAClD", "ignoreList": [0]}