const mongoose = require('mongoose');

const orderItemSchema = new mongoose.Schema({
  food: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Food',
    required: true
  },
  name: String, // Store name for historical purposes
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  variant: {
    name: String,
    price: Number
  },
  addons: [{
    name: String,
    price: Number,
    quantity: { type: Number, default: 1 }
  }],
  customizations: [{
    name: String,
    value: String
  }],
  specialInstructions: String,
  itemTotal: {
    type: Number,
    required: true,
    min: 0
  }
});

const deliveryAddressSchema = new mongoose.Schema({
  street: { type: String, required: true },
  city: { type: String, required: true },
  state: { type: String, required: true },
  zipCode: { type: String, required: true },
  landmark: String,
  coordinates: {
    latitude: Number,
    longitude: Number
  },
  contactName: String,
  contactPhone: String
});

const paymentSchema = new mongoose.Schema({
  method: {
    type: String,
    enum: ['cash', 'card', 'upi', 'wallet', 'netbanking'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'refunded'],
    default: 'pending'
  },
  transactionId: String,
  paymentGateway: String,
  amount: Number,
  paidAt: Date,
  refundedAt: Date,
  refundAmount: Number,
  refundReason: String
});

const trackingSchema = new mongoose.Schema({
  status: {
    type: String,
    enum: [
      'placed', 'confirmed', 'preparing', 'ready', 
      'picked-up', 'out-for-delivery', 'delivered', 
      'cancelled', 'refunded'
    ],
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  message: String,
  location: {
    latitude: Number,
    longitude: Number
  },
  estimatedTime: Date
});

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  restaurant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Restaurant',
    required: true
  },
  items: [orderItemSchema],
  orderType: {
    type: String,
    enum: ['delivery', 'pickup', 'dine-in'],
    default: 'delivery'
  },
  deliveryAddress: deliveryAddressSchema,
  pricing: {
    subtotal: {
      type: Number,
      required: true,
      min: 0
    },
    deliveryFee: {
      type: Number,
      default: 0,
      min: 0
    },
    packagingFee: {
      type: Number,
      default: 0,
      min: 0
    },
    taxes: {
      type: Number,
      default: 0,
      min: 0
    },
    discount: {
      amount: { type: Number, default: 0 },
      couponCode: String,
      description: String
    },
    tip: {
      type: Number,
      default: 0,
      min: 0
    },
    total: {
      type: Number,
      required: true,
      min: 0
    }
  },
  payment: paymentSchema,
  status: {
    type: String,
    enum: [
      'placed', 'confirmed', 'preparing', 'ready', 
      'picked-up', 'out-for-delivery', 'delivered', 
      'cancelled', 'refunded'
    ],
    default: 'placed'
  },
  tracking: [trackingSchema],
  timing: {
    placedAt: {
      type: Date,
      default: Date.now
    },
    confirmedAt: Date,
    preparationStartedAt: Date,
    readyAt: Date,
    pickedUpAt: Date,
    deliveredAt: Date,
    estimatedDeliveryTime: Date,
    actualDeliveryTime: Date
  },
  deliveryPerson: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  specialInstructions: String,
  rating: {
    food: { type: Number, min: 1, max: 5 },
    delivery: { type: Number, min: 1, max: 5 },
    overall: { type: Number, min: 1, max: 5 },
    comment: String,
    ratedAt: Date
  },
  cancellation: {
    reason: String,
    cancelledBy: {
      type: String,
      enum: ['customer', 'restaurant', 'admin', 'system']
    },
    cancelledAt: Date,
    refundStatus: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'pending'
    }
  },
  loyaltyPointsEarned: {
    type: Number,
    default: 0
  },
  isReorder: {
    type: Boolean,
    default: false
  },
  originalOrder: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order'
  },
  notifications: {
    customer: {
      sms: { type: Boolean, default: false },
      email: { type: Boolean, default: false },
      push: { type: Boolean, default: false }
    },
    restaurant: {
      sms: { type: Boolean, default: false },
      email: { type: Boolean, default: false }
    }
  }
}, {
  timestamps: true
});

// Indexes for better performance
orderSchema.index({ customer: 1, createdAt: -1 });
orderSchema.index({ restaurant: 1, createdAt: -1 });
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ 'payment.status': 1 });
orderSchema.index({ deliveryPerson: 1 });

// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  if (this.isNew) {
    const count = await mongoose.model('Order').countDocuments();
    this.orderNumber = `ORD${Date.now()}${(count + 1).toString().padStart(4, '0')}`;
  }
  next();
});

// Method to update status and add tracking
orderSchema.methods.updateStatus = function(newStatus, message, location) {
  this.status = newStatus;
  
  // Add to tracking
  this.tracking.push({
    status: newStatus,
    message: message,
    location: location,
    timestamp: new Date()
  });
  
  // Update timing
  const now = new Date();
  switch (newStatus) {
    case 'confirmed':
      this.timing.confirmedAt = now;
      break;
    case 'preparing':
      this.timing.preparationStartedAt = now;
      break;
    case 'ready':
      this.timing.readyAt = now;
      break;
    case 'picked-up':
      this.timing.pickedUpAt = now;
      break;
    case 'delivered':
      this.timing.deliveredAt = now;
      this.timing.actualDeliveryTime = now;
      break;
  }
  
  return this.save();
};

// Method to calculate estimated delivery time
orderSchema.methods.calculateEstimatedDeliveryTime = function() {
  const baseTime = 30; // 30 minutes base time
  const itemCount = this.items.reduce((sum, item) => sum + item.quantity, 0);
  const additionalTime = Math.ceil(itemCount / 5) * 5; // 5 minutes per 5 items
  
  const estimatedMinutes = baseTime + additionalTime;
  this.timing.estimatedDeliveryTime = new Date(Date.now() + estimatedMinutes * 60000);
  
  return this.timing.estimatedDeliveryTime;
};

// Method to check if order can be cancelled
orderSchema.methods.canBeCancelled = function() {
  const nonCancellableStatuses = ['picked-up', 'out-for-delivery', 'delivered', 'cancelled'];
  return !nonCancellableStatuses.includes(this.status);
};

// Virtual for order age in minutes
orderSchema.virtual('ageInMinutes').get(function() {
  return Math.floor((Date.now() - this.timing.placedAt) / 60000);
});

// Transform output
orderSchema.methods.toJSON = function() {
  const order = this.toObject();
  order.id = order._id;
  order.ageInMinutes = this.ageInMinutes;
  delete order._id;
  delete order.__v;
  return order;
};

module.exports = mongoose.model('Order', orderSchema);
