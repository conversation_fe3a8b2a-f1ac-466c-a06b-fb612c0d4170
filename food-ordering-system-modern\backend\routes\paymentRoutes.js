const express = require('express');
const { protect } = require('../middleware/auth');

const router = express.Router();

// @desc    Process payment
// @route   POST /api/payments/process
// @access  Private
router.post('/process', protect, async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      message: 'Payment processing endpoint',
      data: {}
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
