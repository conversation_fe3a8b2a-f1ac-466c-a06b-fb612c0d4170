const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// @desc    Admin dashboard stats
// @route   GET /api/admin/stats
// @access  Private (Admin)
router.get('/stats', protect, authorize('admin'), async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      message: 'Admin stats endpoint',
      data: {}
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
