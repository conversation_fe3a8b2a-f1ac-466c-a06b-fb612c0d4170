const mongoose = require('mongoose');

const operatingHoursSchema = new mongoose.Schema({
  day: {
    type: String,
    enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
    required: true
  },
  isOpen: {
    type: Boolean,
    default: true
  },
  openTime: {
    type: String,
    required: function() { return this.isOpen; }
  },
  closeTime: {
    type: String,
    required: function() { return this.isOpen; }
  }
});

const locationSchema = new mongoose.Schema({
  address: {
    street: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    zipCode: { type: String, required: true },
    country: { type: String, default: 'India' }
  },
  coordinates: {
    latitude: { type: Number, required: true },
    longitude: { type: Number, required: true }
  },
  deliveryRadius: {
    type: Number,
    default: 5, // in kilometers
    min: 1,
    max: 50
  }
});

const restaurantSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Restaurant name is required'],
    trim: true,
    maxlength: [100, 'Restaurant name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    match: [/^\d{10}$/, 'Please enter a valid 10-digit phone number']
  },
  images: {
    logo: String,
    banner: String,
    gallery: [String]
  },
  cuisine: [{
    type: String,
    required: true
  }],
  location: {
    type: locationSchema,
    required: true
  },
  operatingHours: [operatingHoursSchema],
  pricing: {
    deliveryFee: {
      type: Number,
      default: 0,
      min: 0
    },
    minimumOrder: {
      type: Number,
      default: 0,
      min: 0
    },
    packagingFee: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  ratings: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  features: {
    isVegetarian: { type: Boolean, default: false },
    hasVeganOptions: { type: Boolean, default: false },
    acceptsOnlinePayment: { type: Boolean, default: true },
    hasDelivery: { type: Boolean, default: true },
    hasPickup: { type: Boolean, default: true },
    hasDineIn: { type: Boolean, default: false }
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'suspended', 'closed'],
    default: 'pending'
  },
  isOpen: {
    type: Boolean,
    default: true
  },
  preparationTime: {
    type: Number,
    default: 30, // in minutes
    min: 10,
    max: 120
  },
  totalOrders: {
    type: Number,
    default: 0
  },
  totalRevenue: {
    type: Number,
    default: 0
  },
  documents: {
    businessLicense: String,
    foodLicense: String,
    taxId: String
  },
  socialMedia: {
    website: String,
    facebook: String,
    instagram: String,
    twitter: String
  },
  specialOffers: [{
    title: String,
    description: String,
    discount: Number,
    validFrom: Date,
    validTo: Date,
    isActive: { type: Boolean, default: true }
  }],
  tags: [String],
  isPromoted: {
    type: Boolean,
    default: false
  },
  promotionExpiry: Date
}, {
  timestamps: true
});

// Indexes for better performance
restaurantSchema.index({ 'location.coordinates': '2dsphere' });
restaurantSchema.index({ cuisine: 1 });
restaurantSchema.index({ status: 1 });
restaurantSchema.index({ 'ratings.average': -1 });
restaurantSchema.index({ isOpen: 1 });
restaurantSchema.index({ name: 'text', description: 'text' });

// Virtual for average rating
restaurantSchema.virtual('averageRating').get(function() {
  return this.ratings.average;
});

// Method to check if restaurant is currently open
restaurantSchema.methods.isCurrentlyOpen = function() {
  if (!this.isOpen) return false;
  
  const now = new Date();
  const currentDay = now.toLocaleLowerCase().substring(0, 3); // mon, tue, etc.
  const currentTime = now.toTimeString().substring(0, 5); // HH:MM format
  
  const todayHours = this.operatingHours.find(hours => 
    hours.day.substring(0, 3) === currentDay
  );
  
  if (!todayHours || !todayHours.isOpen) return false;
  
  return currentTime >= todayHours.openTime && currentTime <= todayHours.closeTime;
};

// Method to calculate distance from a point
restaurantSchema.methods.calculateDistance = function(lat, lng) {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat - this.location.coordinates.latitude) * Math.PI / 180;
  const dLng = (lng - this.location.coordinates.longitude) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(this.location.coordinates.latitude * Math.PI / 180) * 
    Math.cos(lat * Math.PI / 180) * Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

// Method to check if location is within delivery radius
restaurantSchema.methods.canDeliverTo = function(lat, lng) {
  const distance = this.calculateDistance(lat, lng);
  return distance <= this.location.deliveryRadius;
};

// Method to update rating
restaurantSchema.methods.updateRating = function(newRating) {
  const totalRating = (this.ratings.average * this.ratings.count) + newRating;
  this.ratings.count += 1;
  this.ratings.average = totalRating / this.ratings.count;
  return this.save();
};

// Transform output
restaurantSchema.methods.toJSON = function() {
  const restaurant = this.toObject();
  restaurant.id = restaurant._id;
  delete restaurant._id;
  delete restaurant.__v;
  return restaurant;
};

module.exports = mongoose.model('Restaurant', restaurantSchema);
