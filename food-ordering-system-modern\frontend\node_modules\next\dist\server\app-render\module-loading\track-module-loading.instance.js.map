{"version": 3, "sources": ["../../../../src/server/app-render/module-loading/track-module-loading.instance.ts"], "sourcesContent": ["import { CacheSignal } from '../cache-signal'\nimport { isThenable } from '../../../shared/lib/is-thenable'\n\n/**\n * Tracks all in-flight async imports and chunk loads.\n * Initialized lazily, because we don't want this to error in case it gets pulled into an edge runtime module.\n */\nlet _moduleLoadingSignal: CacheSignal | null\nfunction getModuleLoadingSignal() {\n  if (!_moduleLoadingSignal) {\n    _moduleLoadingSignal = new CacheSignal()\n  }\n  return _moduleLoadingSignal\n}\n\nexport function trackPendingChunkLoad(promise: Promise<unknown>) {\n  const moduleLoadingSignal = getModuleLoadingSignal()\n  moduleLoadingSignal.trackRead(promise)\n}\n\nexport function trackPendingImport(exportsOrPromise: unknown) {\n  const moduleLoadingSignal = getModuleLoadingSignal()\n\n  // requiring an async module returns a promise.\n  // if it's sync, there's nothing to track.\n  if (isThenable(exportsOrPromise)) {\n    // A client reference proxy might look like a promise, but we can only call `.then()` on it, not e.g. `.finally()`.\n    // Turn it into a real promise to avoid issues elsewhere.\n    const promise = Promise.resolve(exportsOrPromise)\n    moduleLoadingSignal.trackRead(promise)\n  }\n}\n\n/**\n * A top-level dynamic import (or chunk load):\n *\n *   1. delays a prerender (potentially for a task or longer)\n *   2. may reveal more caches that need be filled\n *\n * So if we see one, we want to extend the duration of `cacheSignal` at least until the import/chunk-load is done.\n */\nexport function trackPendingModules(cacheSignal: CacheSignal): void {\n  const moduleLoadingSignal = getModuleLoadingSignal()\n\n  // We can't just use `cacheSignal.trackRead(moduleLoadingSignal.cacheReady())`,\n  // because we might start and finish multiple batches of module loads while waiting for caches,\n  // and `moduleLoadingSignal.cacheReady()` would resolve after the first batch.\n  // Instead, we'll keep notifying `cacheSignal` of each import/chunk-load.\n  const unsubscribe = moduleLoadingSignal.subscribeToReads(cacheSignal)\n\n  // Later, when `cacheSignal` is no longer waiting for any caches (or imports that we've notified it of),\n  // we can unsubscribe it.\n  cacheSignal.cacheReady().then(unsubscribe)\n}\n"], "names": ["trackPendingChunkLoad", "trackPendingImport", "trackPendingModules", "_moduleLoadingSignal", "getModuleLoadingSignal", "CacheSignal", "promise", "moduleLoadingSignal", "trackRead", "exportsOrPromise", "isThenable", "Promise", "resolve", "cacheSignal", "unsubscribe", "subscribeToReads", "cacheReady", "then"], "mappings": ";;;;;;;;;;;;;;;;IAegBA,qBAAqB;eAArBA;;IAKAC,kBAAkB;eAAlBA;;IAqBAC,mBAAmB;eAAnBA;;;6BAzCY;4BACD;AAE3B;;;CAGC,GACD,IAAIC;AACJ,SAASC;IACP,IAAI,CAACD,sBAAsB;QACzBA,uBAAuB,IAAIE,wBAAW;IACxC;IACA,OAAOF;AACT;AAEO,SAASH,sBAAsBM,OAAyB;IAC7D,MAAMC,sBAAsBH;IAC5BG,oBAAoBC,SAAS,CAACF;AAChC;AAEO,SAASL,mBAAmBQ,gBAAyB;IAC1D,MAAMF,sBAAsBH;IAE5B,+CAA+C;IAC/C,0CAA0C;IAC1C,IAAIM,IAAAA,sBAAU,EAACD,mBAAmB;QAChC,mHAAmH;QACnH,yDAAyD;QACzD,MAAMH,UAAUK,QAAQC,OAAO,CAACH;QAChCF,oBAAoBC,SAAS,CAACF;IAChC;AACF;AAUO,SAASJ,oBAAoBW,WAAwB;IAC1D,MAAMN,sBAAsBH;IAE5B,+EAA+E;IAC/E,+FAA+F;IAC/F,8EAA8E;IAC9E,yEAAyE;IACzE,MAAMU,cAAcP,oBAAoBQ,gBAAgB,CAACF;IAEzD,wGAAwG;IACxG,yBAAyB;IACzBA,YAAYG,UAAU,GAAGC,IAAI,CAACH;AAChC", "ignoreList": [0]}