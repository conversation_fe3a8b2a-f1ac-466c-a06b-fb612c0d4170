{"version": 3, "sources": ["../../../../src/server/app-render/module-loading/track-module-loading.external.ts"], "sourcesContent": ["// NOTE: this is marked as shared/external because it's stateful\n// and the state needs to be shared between app-render (which waits for pending imports)\n// and helpers used in transformed page code (which register pending imports)\n\nimport {\n  trackPendingChunkLoad,\n  trackPendingImport,\n  trackPendingModules,\n} from './track-module-loading.instance' with { 'turbopack-transition': 'next-shared' }\n\nexport { trackPendingChunkLoad, trackPendingImport, trackPendingModules }\n"], "names": ["trackPendingChunkLoad", "trackPendingImport", "trackPendingModules"], "mappings": "AAAA,gEAAgE;AAChE,wFAAwF;AACxF,6EAA6E;AAE7E,SACEA,qBAAqB,EACrBC,kBAAkB,EAClBC,mBAAmB,QACd,uCAAuC;IAAE,wBAAwB;AAAc,EAAC;AAEvF,SAASF,qBAAqB,EAAEC,kBAAkB,EAAEC,mBAAmB,GAAE", "ignoreList": [0]}