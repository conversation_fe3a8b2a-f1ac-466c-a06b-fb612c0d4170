{"version": 3, "sources": ["../../../../src/server/api-utils/node/try-get-preview-data.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextApiResponse } from '../../../shared/lib/utils'\nimport { checkIsOnDemandRevalidate } from '../.'\nimport type { __ApiPreviewProps } from '../.'\nimport type { BaseNextRequest, BaseNextResponse } from '../../base-http'\nimport type { PreviewData } from '../../../types'\n\nimport {\n  clearPreviewData,\n  COOKIE_NAME_PRERENDER_BYPASS,\n  COOKIE_NAME_PRERENDER_DATA,\n  SYMBOL_PREVIEW_DATA,\n} from '../index'\nimport { RequestCookies } from '../../web/spec-extension/cookies'\nimport { HeadersAdapter } from '../../web/spec-extension/adapters/headers'\n\nexport function tryGetPreviewData(\n  req: IncomingMessage | BaseNextRequest | Request,\n  res: ServerResponse | BaseNextResponse,\n  options: __ApiPreviewProps,\n  multiZoneDraftMode: boolean\n): PreviewData {\n  // if an On-Demand revalidation is being done preview mode\n  // is disabled\n  if (options && checkIsOnDemandRevalidate(req, options).isOnDemandRevalidate) {\n    return false\n  }\n\n  // Read cached preview data if present\n  // TODO: use request metadata instead of a symbol\n  if (SYMBOL_PREVIEW_DATA in req) {\n    return (req as any)[SYMBOL_PREVIEW_DATA] as any\n  }\n\n  const headers = HeadersAdapter.from(req.headers)\n  const cookies = new RequestCookies(headers)\n\n  const previewModeId = cookies.get(COOKIE_NAME_PRERENDER_BYPASS)?.value\n  const tokenPreviewData = cookies.get(COOKIE_NAME_PRERENDER_DATA)?.value\n\n  // Case: preview mode cookie set but data cookie is not set\n  if (\n    previewModeId &&\n    !tokenPreviewData &&\n    previewModeId === options.previewModeId\n  ) {\n    // This is \"Draft Mode\" which doesn't use\n    // previewData, so we return an empty object\n    // for backwards compat with \"Preview Mode\".\n    const data = {}\n    Object.defineProperty(req, SYMBOL_PREVIEW_DATA, {\n      value: data,\n      enumerable: false,\n    })\n    return data\n  }\n\n  // Case: neither cookie is set.\n  if (!previewModeId && !tokenPreviewData) {\n    return false\n  }\n\n  // Case: one cookie is set, but not the other.\n  if (!previewModeId || !tokenPreviewData) {\n    if (!multiZoneDraftMode) {\n      clearPreviewData(res as NextApiResponse)\n    }\n    return false\n  }\n\n  // Case: preview session is for an old build.\n  if (previewModeId !== options.previewModeId) {\n    if (!multiZoneDraftMode) {\n      clearPreviewData(res as NextApiResponse)\n    }\n    return false\n  }\n\n  let encryptedPreviewData: {\n    data: string\n  }\n  try {\n    const jsonwebtoken =\n      require('next/dist/compiled/jsonwebtoken') as typeof import('next/dist/compiled/jsonwebtoken')\n    encryptedPreviewData = jsonwebtoken.verify(\n      tokenPreviewData,\n      options.previewModeSigningKey\n    ) as typeof encryptedPreviewData\n  } catch {\n    // TODO: warn\n    clearPreviewData(res as NextApiResponse)\n    return false\n  }\n\n  const { decryptWithSecret } =\n    require('../../crypto-utils') as typeof import('../../crypto-utils')\n  const decryptedPreviewData = decryptWithSecret(\n    Buffer.from(options.previewModeEncryptionKey),\n    encryptedPreviewData.data\n  )\n\n  try {\n    // TODO: strict runtime type checking\n    const data = JSON.parse(decryptedPreviewData)\n    // Cache lookup\n    Object.defineProperty(req, SYMBOL_PREVIEW_DATA, {\n      value: data,\n      enumerable: false,\n    })\n    return data\n  } catch {\n    return false\n  }\n}\n"], "names": ["checkIsOnDemandRevalidate", "clearPreviewData", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "SYMBOL_PREVIEW_DATA", "RequestCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tryGetPreviewData", "req", "res", "options", "multiZoneDraftMode", "cookies", "isOnDemandRevalidate", "headers", "from", "previewModeId", "get", "value", "tokenPreviewData", "data", "Object", "defineProperty", "enumerable", "encryptedPreviewData", "jsonwebtoken", "require", "verify", "previewModeSigningKey", "decryptWithSecret", "decryptedPreviewData", "<PERSON><PERSON><PERSON>", "previewModeEncryptionKey", "JSON", "parse"], "mappings": "AAEA,SAASA,yBAAyB,QAAQ,OAAM;AAKhD,SACEC,gBAAgB,EAChBC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,mBAAmB,QACd,WAAU;AACjB,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,cAAc,QAAQ,4CAA2C;AAE1E,OAAO,SAASC,kBACdC,GAAgD,EAChDC,GAAsC,EACtCC,OAA0B,EAC1BC,kBAA2B;QAiBLC,cACGA;IAhBzB,0DAA0D;IAC1D,cAAc;IACd,IAAIF,WAAWV,0BAA0BQ,KAAKE,SAASG,oBAAoB,EAAE;QAC3E,OAAO;IACT;IAEA,sCAAsC;IACtC,iDAAiD;IACjD,IAAIT,uBAAuBI,KAAK;QAC9B,OAAO,AAACA,GAAW,CAACJ,oBAAoB;IAC1C;IAEA,MAAMU,UAAUR,eAAeS,IAAI,CAACP,IAAIM,OAAO;IAC/C,MAAMF,UAAU,IAAIP,eAAeS;IAEnC,MAAME,iBAAgBJ,eAAAA,QAAQK,GAAG,CAACf,kDAAZU,aAA2CM,KAAK;IACtE,MAAMC,oBAAmBP,gBAAAA,QAAQK,GAAG,CAACd,gDAAZS,cAAyCM,KAAK;IAEvE,2DAA2D;IAC3D,IACEF,iBACA,CAACG,oBACDH,kBAAkBN,QAAQM,aAAa,EACvC;QACA,yCAAyC;QACzC,4CAA4C;QAC5C,4CAA4C;QAC5C,MAAMI,OAAO,CAAC;QACdC,OAAOC,cAAc,CAACd,KAAKJ,qBAAqB;YAC9Cc,OAAOE;YACPG,YAAY;QACd;QACA,OAAOH;IACT;IAEA,+BAA+B;IAC/B,IAAI,CAACJ,iBAAiB,CAACG,kBAAkB;QACvC,OAAO;IACT;IAEA,8CAA8C;IAC9C,IAAI,CAACH,iBAAiB,CAACG,kBAAkB;QACvC,IAAI,CAACR,oBAAoB;YACvBV,iBAAiBQ;QACnB;QACA,OAAO;IACT;IAEA,6CAA6C;IAC7C,IAAIO,kBAAkBN,QAAQM,aAAa,EAAE;QAC3C,IAAI,CAACL,oBAAoB;YACvBV,iBAAiBQ;QACnB;QACA,OAAO;IACT;IAEA,IAAIe;IAGJ,IAAI;QACF,MAAMC,eACJC,QAAQ;QACVF,uBAAuBC,aAAaE,MAAM,CACxCR,kBACAT,QAAQkB,qBAAqB;IAEjC,EAAE,OAAM;QACN,aAAa;QACb3B,iBAAiBQ;QACjB,OAAO;IACT;IAEA,MAAM,EAAEoB,iBAAiB,EAAE,GACzBH,QAAQ;IACV,MAAMI,uBAAuBD,kBAC3BE,OAAOhB,IAAI,CAACL,QAAQsB,wBAAwB,GAC5CR,qBAAqBJ,IAAI;IAG3B,IAAI;QACF,qCAAqC;QACrC,MAAMA,OAAOa,KAAKC,KAAK,CAACJ;QACxB,eAAe;QACfT,OAAOC,cAAc,CAACd,KAAKJ,qBAAqB;YAC9Cc,OAAOE;YACPG,YAAY;QACd;QACA,OAAOH;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF", "ignoreList": [0]}