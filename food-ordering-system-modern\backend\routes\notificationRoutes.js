const express = require('express');
const { protect } = require('../middleware/auth');

const router = express.Router();

// @desc    Get user notifications
// @route   GET /api/notifications
// @access  Private
router.get('/', protect, async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      message: 'Notifications endpoint',
      data: []
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
