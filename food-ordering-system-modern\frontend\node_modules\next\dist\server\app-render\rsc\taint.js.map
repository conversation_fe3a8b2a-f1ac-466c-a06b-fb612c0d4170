{"version": 3, "sources": ["../../../../src/server/app-render/rsc/taint.ts"], "sourcesContent": ["/*\n\nFiles in the rsc directory are meant to be packaged as part of the RSC graph using next-app-loader.\n\n*/\n\nimport * as React from 'react'\n\ntype Reference = object\ntype TaintableUniqueValue = string | bigint | ArrayBufferView\n\nfunction notImplemented() {\n  throw new Error('Taint can only be used with the taint flag.')\n}\n\nexport const taintObjectReference: (\n  message: string | undefined,\n  object: Reference\n) => void = process.env.__NEXT_EXPERIMENTAL_REACT\n  ? // @ts-ignore\n    React.experimental_taintObjectReference\n  : notImplemented\nexport const taintUniqueValue: (\n  message: string | undefined,\n  lifetime: Reference,\n  value: TaintableUniqueValue\n) => void = process.env.__NEXT_EXPERIMENTAL_REACT\n  ? // @ts-ignore\n    React.experimental_taintUniqueValue\n  : notImplemented\n"], "names": ["taintObjectReference", "taintUniqueValue", "notImplemented", "Error", "process", "env", "__NEXT_EXPERIMENTAL_REACT", "React", "experimental_taintObjectReference", "experimental_taintUniqueValue"], "mappings": "AAAA;;;;AAIA;;;;;;;;;;;;;;;IAWaA,oBAAoB;eAApBA;;IAOAC,gBAAgB;eAAhBA;;;+DAhBU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKvB,SAASC;IACP,MAAM,qBAAwD,CAAxD,IAAIC,MAAM,gDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAuD;AAC/D;AAEO,MAAMH,uBAGDI,QAAQC,GAAG,CAACC,yBAAyB,GAE7CC,OAAMC,iCAAiC,GACvCN;AACG,MAAMD,mBAIDG,QAAQC,GAAG,CAACC,yBAAyB,GAE7CC,OAAME,6BAA6B,GACnCP", "ignoreList": [0]}