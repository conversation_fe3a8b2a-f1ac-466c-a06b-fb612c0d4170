const express = require('express');
const Food = require('../models/Food');
const { protect, authorize, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// @desc    Get all foods
// @route   GET /api/foods
// @access  Public
router.get('/', optionalAuth, async (req, res, next) => {
  try {
    const { page = 1, limit = 20, category, restaurant, search } = req.query;
    
    const query = { isAvailable: true };
    
    if (category) query.category = category;
    if (restaurant) query.restaurant = restaurant;
    if (search) query.$text = { $search: search };

    const foods = await Food.find(query)
      .populate('restaurant', 'name location ratings')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ orderCount: -1 });

    const total = await Food.countDocuments(query);

    res.status(200).json({
      success: true,
      count: foods.length,
      total,
      data: foods
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get single food
// @route   GET /api/foods/:id
// @access  Public
router.get('/:id', async (req, res, next) => {
  try {
    const food = await Food.findById(req.params.id).populate('restaurant');
    
    if (!food) {
      return res.status(404).json({
        success: false,
        message: 'Food item not found'
      });
    }

    res.status(200).json({
      success: true,
      data: food
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
