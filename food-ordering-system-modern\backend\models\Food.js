const mongoose = require('mongoose');

const nutritionSchema = new mongoose.Schema({
  calories: Number,
  protein: Number, // in grams
  carbs: Number, // in grams
  fat: Number, // in grams
  fiber: Number, // in grams
  sugar: Number, // in grams
  sodium: Number // in mg
});

const variantSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  description: String,
  isAvailable: {
    type: Boolean,
    default: true
  }
});

const addonSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  category: String,
  isRequired: {
    type: Boolean,
    default: false
  },
  maxQuantity: {
    type: Number,
    default: 1
  }
});

const foodSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Food name is required'],
    trim: true,
    maxlength: [100, 'Food name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  restaurant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Restaurant',
    required: true
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'appetizers', 'main-course', 'desserts', 'beverages', 
      'salads', 'soups', 'snacks', 'breakfast', 'lunch', 
      'dinner', 'chinese', 'indian', 'italian', 'mexican', 
      'thai', 'continental', 'fast-food', 'healthy'
    ]
  },
  subcategory: String,
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative']
  },
  originalPrice: {
    type: Number,
    min: [0, 'Original price cannot be negative']
  },
  discount: {
    percentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    validFrom: Date,
    validTo: Date
  },
  images: [{
    type: String,
    required: true
  }],
  variants: [variantSchema],
  addons: [addonSchema],
  ingredients: [String],
  allergens: [String],
  nutrition: nutritionSchema,
  dietaryInfo: {
    isVegetarian: { type: Boolean, default: false },
    isVegan: { type: Boolean, default: false },
    isGlutenFree: { type: Boolean, default: false },
    isKeto: { type: Boolean, default: false },
    isLowCarb: { type: Boolean, default: false },
    isLowFat: { type: Boolean, default: false },
    isHighProtein: { type: Boolean, default: false }
  },
  spiceLevel: {
    type: String,
    enum: ['mild', 'medium', 'hot', 'very-hot'],
    default: 'mild'
  },
  preparationTime: {
    type: Number,
    required: true,
    min: 5,
    max: 120 // in minutes
  },
  servingSize: {
    type: String,
    default: '1 serving'
  },
  isAvailable: {
    type: Boolean,
    default: true
  },
  isPopular: {
    type: Boolean,
    default: false
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  ratings: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  orderCount: {
    type: Number,
    default: 0
  },
  tags: [String],
  customizations: [{
    name: String,
    options: [String],
    isRequired: { type: Boolean, default: false },
    allowMultiple: { type: Boolean, default: false }
  }],
  availability: {
    days: [{
      type: String,
      enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    }],
    timeSlots: [{
      start: String, // HH:MM format
      end: String    // HH:MM format
    }]
  },
  stock: {
    quantity: {
      type: Number,
      default: -1 // -1 means unlimited
    },
    isLimited: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true
});

// Indexes for better performance
foodSchema.index({ restaurant: 1 });
foodSchema.index({ category: 1 });
foodSchema.index({ isAvailable: 1 });
foodSchema.index({ 'ratings.average': -1 });
foodSchema.index({ orderCount: -1 });
foodSchema.index({ price: 1 });
foodSchema.index({ name: 'text', description: 'text' });

// Virtual for discounted price
foodSchema.virtual('discountedPrice').get(function() {
  if (this.discount.percentage > 0) {
    const now = new Date();
    if (this.discount.validFrom <= now && this.discount.validTo >= now) {
      return this.price * (1 - this.discount.percentage / 100);
    }
  }
  return this.price;
});

// Virtual for final price (considering variants)
foodSchema.virtual('finalPrice').get(function() {
  return this.discountedPrice;
});

// Method to check if food is currently available
foodSchema.methods.isCurrentlyAvailable = function() {
  if (!this.isAvailable) return false;
  
  // Check stock
  if (this.stock.isLimited && this.stock.quantity <= 0) return false;
  
  // Check time availability
  if (this.availability.timeSlots.length > 0) {
    const now = new Date();
    const currentTime = now.toTimeString().substring(0, 5);
    const currentDay = now.toLocaleLowerCase().substring(0, 3);
    
    const isDayAvailable = this.availability.days.some(day => 
      day.substring(0, 3) === currentDay
    );
    
    if (!isDayAvailable) return false;
    
    const isTimeAvailable = this.availability.timeSlots.some(slot => 
      currentTime >= slot.start && currentTime <= slot.end
    );
    
    if (!isTimeAvailable) return false;
  }
  
  return true;
};

// Method to update rating
foodSchema.methods.updateRating = function(newRating) {
  const totalRating = (this.ratings.average * this.ratings.count) + newRating;
  this.ratings.count += 1;
  this.ratings.average = totalRating / this.ratings.count;
  return this.save();
};

// Method to decrease stock
foodSchema.methods.decreaseStock = function(quantity = 1) {
  if (this.stock.isLimited) {
    this.stock.quantity = Math.max(0, this.stock.quantity - quantity);
  }
  this.orderCount += quantity;
  return this.save();
};

// Transform output
foodSchema.methods.toJSON = function() {
  const food = this.toObject();
  food.id = food._id;
  food.discountedPrice = this.discountedPrice;
  food.finalPrice = this.finalPrice;
  delete food._id;
  delete food.__v;
  return food;
};

module.exports = mongoose.model('Food', foodSchema);
