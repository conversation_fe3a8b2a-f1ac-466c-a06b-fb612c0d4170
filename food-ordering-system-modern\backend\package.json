{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.8.1", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemailer": "^7.0.5", "nodemon": "^3.1.10", "socket.io": "^4.8.1", "stripe": "^18.3.0"}}