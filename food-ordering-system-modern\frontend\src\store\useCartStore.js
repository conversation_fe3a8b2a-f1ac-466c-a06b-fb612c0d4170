import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { toast } from 'react-hot-toast';

const useCartStore = create(
  persist(
    (set, get) => ({
      items: [],
      restaurant: null,
      isOpen: false,

      // Add item to cart
      addItem: (food, quantity = 1, variant = null, addons = [], customizations = []) => {
        const state = get();
        
        // Check if item is from different restaurant
        if (state.restaurant && state.restaurant._id !== food.restaurant._id) {
          toast.error('You can only order from one restaurant at a time');
          return false;
        }

        // Create item object
        const newItem = {
          id: `${food._id}-${variant?.name || 'default'}-${Date.now()}`,
          food,
          quantity,
          variant,
          addons: addons || [],
          customizations: customizations || [],
          price: variant?.price || food.price,
          itemTotal: calculateItemTotal(food, quantity, variant, addons)
        };

        set({
          items: [...state.items, newItem],
          restaurant: food.restaurant,
        });

        toast.success(`${food.name} added to cart`);
        return true;
      },

      // Update item quantity
      updateQuantity: (itemId, quantity) => {
        if (quantity <= 0) {
          get().removeItem(itemId);
          return;
        }

        set((state) => ({
          items: state.items.map(item => 
            item.id === itemId 
              ? { 
                  ...item, 
                  quantity,
                  itemTotal: calculateItemTotal(item.food, quantity, item.variant, item.addons)
                }
              : item
          )
        }));
      },

      // Remove item from cart
      removeItem: (itemId) => {
        set((state) => {
          const newItems = state.items.filter(item => item.id !== itemId);
          return {
            items: newItems,
            restaurant: newItems.length === 0 ? null : state.restaurant
          };
        });
        toast.success('Item removed from cart');
      },

      // Clear entire cart
      clearCart: () => {
        set({ items: [], restaurant: null });
        toast.success('Cart cleared');
      },

      // Toggle cart visibility
      toggleCart: () => {
        set((state) => ({ isOpen: !state.isOpen }));
      },

      // Open cart
      openCart: () => {
        set({ isOpen: true });
      },

      // Close cart
      closeCart: () => {
        set({ isOpen: false });
      },

      // Get cart totals
      getTotals: () => {
        const state = get();
        const subtotal = state.items.reduce((sum, item) => sum + item.itemTotal, 0);
        const deliveryFee = state.restaurant?.pricing?.deliveryFee || 0;
        const packagingFee = state.restaurant?.pricing?.packagingFee || 0;
        const taxes = subtotal * 0.18; // 18% GST
        const total = subtotal + deliveryFee + packagingFee + taxes;

        return {
          subtotal,
          deliveryFee,
          packagingFee,
          taxes,
          total,
          itemCount: state.items.reduce((sum, item) => sum + item.quantity, 0)
        };
      },

      // Check if item exists in cart
      isItemInCart: (foodId, variant = null) => {
        const state = get();
        return state.items.some(item => 
          item.food._id === foodId && 
          (variant ? item.variant?.name === variant.name : !item.variant)
        );
      },

      // Get item from cart
      getCartItem: (foodId, variant = null) => {
        const state = get();
        return state.items.find(item => 
          item.food._id === foodId && 
          (variant ? item.variant?.name === variant.name : !item.variant)
        );
      },
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({ 
        items: state.items, 
        restaurant: state.restaurant 
      }),
    }
  )
);

// Helper function to calculate item total
const calculateItemTotal = (food, quantity, variant, addons) => {
  let basePrice = variant?.price || food.price;
  
  // Add addon prices
  const addonTotal = addons?.reduce((sum, addon) => {
    return sum + (addon.price * (addon.quantity || 1));
  }, 0) || 0;

  return (basePrice + addonTotal) * quantity;
};

export default useCartStore;
