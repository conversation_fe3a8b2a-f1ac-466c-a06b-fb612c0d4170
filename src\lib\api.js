import axios from 'axios';
import { toast } from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api',
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const response = await api.post('/auth/refresh');
        const { token } = response.data.data;
        
        localStorage.setItem('token', token);
        originalRequest.headers.Authorization = `Bearer ${token}`;
        
        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/auth/login';
        return Promise.reject(refreshError);
      }
    }

    // Show error toast for non-401 errors
    if (error.response?.data?.message) {
      toast.error(error.response.data.message);
    } else {
      toast.error('Something went wrong. Please try again.');
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (data) => api.post('/auth/register', data),
  login: (data) => api.post('/auth/login', data),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/me'),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (data) => api.put('/auth/reset-password', data),
  refreshToken: () => api.post('/auth/refresh'),
};

// User API
export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data),
  addAddress: (data) => api.post('/users/addresses', data),
  updateAddress: (id, data) => api.put(`/users/addresses/${id}`, data),
  deleteAddress: (id) => api.delete(`/users/addresses/${id}`),
  addToFavorites: (restaurantId) => api.post(`/users/favorites/${restaurantId}`),
  removeFromFavorites: (restaurantId) => api.delete(`/users/favorites/${restaurantId}`),
  getOrders: (params) => api.get('/users/orders', { params }),
  getStats: () => api.get('/users/stats'),
};

// Restaurant API
export const restaurantAPI = {
  getAll: (params) => api.get('/restaurants', { params }),
  getById: (id) => api.get(`/restaurants/${id}`),
  getNearby: (lat, lng, params) => api.get(`/restaurants/nearby/${lat}/${lng}`, { params }),
  create: (data) => api.post('/restaurants', data),
  update: (id, data) => api.put(`/restaurants/${id}`, data),
  delete: (id) => api.delete(`/restaurants/${id}`),
  toggleStatus: (id) => api.patch(`/restaurants/${id}/toggle-status`),
};

// Food API
export const foodAPI = {
  getAll: (params) => api.get('/foods', { params }),
  getById: (id) => api.get(`/foods/${id}`),
  getByRestaurant: (restaurantId, params) => api.get(`/foods/restaurant/${restaurantId}`, { params }),
  create: (data) => api.post('/foods', data),
  update: (id, data) => api.put(`/foods/${id}`, data),
  delete: (id) => api.delete(`/foods/${id}`),
  toggleAvailability: (id) => api.patch(`/foods/${id}/toggle-availability`),
};

// Order API
export const orderAPI = {
  create: (data) => api.post('/orders', data),
  getAll: (params) => api.get('/orders', { params }),
  getById: (id) => api.get(`/orders/${id}`),
  updateStatus: (id, data) => api.patch(`/orders/${id}/status`, data),
  cancel: (id, reason) => api.patch(`/orders/${id}/cancel`, { reason }),
};

// Payment API
export const paymentAPI = {
  process: (data) => api.post('/payments/process', data),
  getMethods: () => api.get('/payments/methods'),
  getHistory: (params) => api.get('/payments/history', { params }),
  refund: (data) => api.post('/payments/refund', data),
};

// Admin API
export const adminAPI = {
  getStats: (params) => api.get('/admin/stats', { params }),
  getUsers: (params) => api.get('/admin/users', { params }),
  getRestaurants: (params) => api.get('/admin/restaurants', { params }),
  updateRestaurantStatus: (id, status) => api.patch(`/admin/restaurants/${id}/status`, { status }),
  toggleUserStatus: (id) => api.patch(`/admin/users/${id}/toggle-status`),
};

// Vendor API
export const vendorAPI = {
  getDashboard: (params) => api.get('/vendors/dashboard', { params }),
  getOrders: (params) => api.get('/vendors/orders', { params }),
  getFoods: (params) => api.get('/vendors/foods', { params }),
  getRestaurant: () => api.get('/vendors/restaurant'),
};

export default api;
