# Server Configuration
NODE_ENV=development
PORT=5000

# Database
MONGODB_URI=mongodb://localhost:27017/food-order

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here
JWT_COOKIE_EXPIRE=7

# Session Configuration
SESSION_SECRET=your-session-secret-here

# Frontend URL
FRONTEND_URL=http://localhost:3000

# Email Configuration (for notifications)
EMAIL_FROM=<EMAIL>
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# SMS Configuration (for notifications)
SMS_API_KEY=your-sms-api-key
SMS_SENDER_ID=FOODAPP

# Payment Gateway (Stripe)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# File Upload
MAX_FILE_SIZE=10485760
FILE_UPLOAD_PATH=./uploads

# Redis (for caching - optional)
REDIS_URL=redis://localhost:6379

# Google Maps API (for location services)
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Firebase (for push notifications - optional)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY=your-firebase-private-key
FIREBASE_CLIENT_EMAIL=your-firebase-client-email
